#include <yaEngineNext/nxt_engine.h>
#include <yaEngineNext/nxt_util.h>
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <yaBasicUtils/filesystem.hpp>
#include <yaBasicUtils/allocator.hpp>

#include <set>
#include <vector>
#include <chrono>
#include <functional>
#include <string>
#include <errno.h>

#include <pcap/pcap.h>
#include <glib-2.0/glib.h>
#include <string.h>
#include <libgen.h> // for api basename
#include <signal.h>
#include <poll.h>

enum work_mode_e
{
    WM_NONE,
    WM_PCAP,
    WM_LIVE,
};

struct app_config
{
    char          *ifName;
    char          *pcapPath;
    int            pcapPreload;
    int            disableOutput;
    int            dumpErrPkt;
    int            runMode;
    char          *trailer;
    char          *linkName;
    char          *dumpPcapPath;
    char          *filterProto;
    pcap_t        *dumpTcpReassPcap;
    pcap_t        *liveCap;
    pcap_dumper_t *dumpDumper;
};

struct app_main_ctx
{
    nxt_engine_t          *engine;
    work_mode_e            workMode;
    int                    precordCnt;
    int                    packetCnt;
    bool                   stop;
    pcap_t                *errorPktPcap;
    pcap_dumper_t         *errorPktDumper;
    app_config             cfg;
    std::set<std::string>  filterProtoSet;
};

app_main_ctx gCtx;

void handle_signal_stop(int signum _U_)
{
    gCtx.stop = 1;
    if (gCtx.workMode == WM_LIVE)
    {
        pcap_breakloop(gCtx.cfg.liveCap);
    }
}

int pcap_open_dumper(const char *filePath, pcap_t **ppPcap, pcap_dumper_t **ppDumper);
int pcap_close_dumper(pcap_t *pcap, pcap_dumper_t *dumper);

void dump_mbuf_to_pdumper(nxt_mbuf_t *mbuf, void *userdata)
{
    pcap_dumper_t *dumper = (pcap_dumper_t*)(userdata);
    pcap_pkthdr pkthdr = {{time(0), 0}, (uint32_t)nxt_mbuf_get_length(mbuf), (uint32_t)nxt_mbuf_get_length(mbuf)};
    pcap_dump((u_char *)dumper, &pkthdr, (const u_char *)nxt_mbuf_get_raw(mbuf, 0));
    // pcap_dump_flush(dumper);
}

void handle_signal_segv(int signum _U_)
{
    pcap_t *pcap = NULL;
    pcap_dumper_t *pdumper = NULL;

    char recentPktPcap[64] = { 0 };
    snprintf(recentPktPcap, sizeof recentPktPcap, "engineNext_recent_pkts_%d.pcap", getpid());

    pcap_open_dumper(recentPktPcap, &pcap, &pdumper);
    nxt_debug_foreach_recent_pkts(gCtx.engine, dump_mbuf_to_pdumper, pdumper);
    pcap_close_dumper(pcap, pdumper);

    exit(-1);
}

int show_proto_record(precord_t *precord, void *userdata)
{
    app_main_ctx *ctx       = (app_main_ctx *)userdata;
    player_t     *layer     = precord_layer_get_top(precord);
    const char   *protoName = precord_layer_get_layer_name(layer);

    if (ctx->cfg.filterProto                                             // filter_proto 不为空，说明需要执行 filter
        && ctx->filterProtoSet.find(protoName) == ctx->filterProtoSet.end()) // 在 filter_proto_set 中找不到，不输出
    {
        return 0;
    }

    nxt_util_show_precord(precord);
    return 0;
}

typedef int (*cb_on_packet_func)(const uint8_t *pktData, uint32_t pktLen, uint64_t timestamp, void *userdata);

struct pcap_packet
{
public:
    pcap_packet()
    {
    }

    pcap_packet(const uint8_t *data, pcap_pkthdr *hdr)
    {
        pktData = new uint8_t[hdr->caplen];
        memcpy(pktData, data, hdr->caplen);
        memcpy(&pktHeader, hdr, sizeof pktHeader);
    }

    pcap_packet(const pcap_packet &other)
    {
        pktData = new uint8_t[other.pktHeader.caplen];
        memcpy(pktData, other.pktData, other.pktHeader.caplen);
        memcpy(&pktHeader, &other.pktHeader, sizeof pktHeader);
    }

    ~pcap_packet()
    {
        delete [] pktData;
    }

    uint8_t    *pktData = NULL;
    pcap_pkthdr pktHeader;
};

int bench_operations(const char *benchName, uint32_t iterateCnt, uint32_t extraNum, std::function<void ()> func)
{
    using namespace std;
    auto start = chrono::steady_clock::now();

    func();

    auto end = chrono::steady_clock::now();
    auto timeDiff = end - start;
    auto duration = chrono::duration_cast<chrono::milliseconds>(timeDiff);

    auto cntPerMilisecond = (float)iterateCnt / duration.count();
    auto qps     = cntPerMilisecond * 1000;

    printf("bench: %-30s(loop %8d-%d) total cost :%5ld ms, qps: %9.2f\n", benchName, iterateCnt, extraNum, duration.count(), qps);

    return iterateCnt;
}

int pcap_packet_foreach(const char *filepath, bool preload _U_, cb_on_packet_func onPacket, void *userdata)
{
    // open pcap
    char errbuf[PCAP_ERRBUF_SIZE] = { 0 };
    pcap_t *pcapHandle = pcap_open_offline(filepath, errbuf);
    if (NULL == pcapHandle)
    {
        printf("pcap_open_offline error:%s\n", errbuf);
        return -1;
    }

    /* pcap 过滤条件 */
    bpf_program    fp;
    char           filterExp[] = "";
    int lSts                   = 0;

    lSts = pcap_compile(pcapHandle, &fp, filterExp, 0, 0);
    CHECK_NOR_EXIT(-1 == lSts, -1, "Couldn't parse filter.\n");
    lSts = pcap_setfilter(pcapHandle, &fp);
    CHECK_NOR_EXIT(-1 == lSts, -1, "Counldn't install filter.\n");

    pcap_pkthdr   *pktHeader = NULL;
    const uint8_t *pktData   = NULL;

    // preload to array then processing them;
    if (preload)
    {
        std::vector<pcap_packet> packetArray;
        for (; !gCtx.stop; )
        {
            lSts = pcap_next_ex(pcapHandle, &pktHeader, &pktData);
            if (0 == lSts || 1 == lSts)
            {  // OK, process it.
                packetArray.emplace_back(pktData, pktHeader);
            }
            else if (-1 == lSts)
            {   // error
                fprintf(stderr, "%s\n", pcap_geterr(pcapHandle));
                break;
            }
            else if (-2 == lSts)
            {   // no more pkt to read from offline file
                break;
            }
        }

        printf("preload done, start processing.\n");

        bench_operations("bench_dissect", packetArray.size(), 0,
                        [&]()
                        {
                            for (auto &pkt : packetArray)
                            {
                                onPacket(pkt.pktData, pkt.pktHeader.caplen, pkt.pktHeader.ts.tv_sec, userdata);
                            }
                        });

        // Free the compiled filter program
        pcap_freecode(&fp);
        pcap_close(pcapHandle);
        return 0;
    }

    // read one packet and process one;
    for (; !gCtx.stop; )
    {
        lSts = pcap_next_ex(pcapHandle, &pktHeader, &pktData);
        if (0 == lSts || 1 == lSts)
        {  // OK, process it.
            onPacket(pktData, pktHeader->caplen, pktHeader->ts.tv_sec, userdata);
        }
        else if (-1 == lSts)
        {   // error
            fprintf(stderr, "%s\n", pcap_geterr(pcapHandle));
            break;
        }
        else if (-2 == lSts)
        {   // no more pkt to read from offline file
            break;
        }
    }

    // Free the compiled filter program
    pcap_freecode(&fp);
    pcap_close(pcapHandle);
    return 0;
}

int nxt_cb_on_packet(const uint8_t *pktData, uint32_t pktLen, uint64_t timestamp _U_, void *userdata)
{
    app_main_ctx *ctx    = (app_main_ctx *)(userdata);
    nxt_engine_t *engine = ctx->engine;
    ya_allocator_t *alloc = nxt_engine_get_allocator(engine);

    nxt_mbuf_t   *mbuf   = nxt_mbuf_new_by_copy_wa(alloc, pktData, pktLen);

    // 输入 mbuf 到 engine 中，推动 engine 运转;
    // TODO: 需要将 init 与 free 放到 nxt_engine_run 中进行;
    nxt_engine_run(engine, mbuf);
    ctx->packetCnt++;
    nxt_mbuf_free_wa(alloc, mbuf);

    return 0;
}

void pcap_live_cb_on_packet(uint8_t *userdata, const struct pcap_pkthdr *pkthdr, const uint8_t *pktdata)
{
    nxt_cb_on_packet(pktdata, pkthdr->caplen, pkthdr->ts.tv_sec, userdata);
}

int live_packet_foreach(const char *liveIf, pcap_handler handler, void *userdata, pcap_t **ppLiveCap)
{
    bpf_program  fp;
    char         errbuf[PCAP_ERRBUF_SIZE] = { 0 };
    char         filterExp[]              = "";
    pcap_t      *pcapHandle               = NULL;
    int          lSts                     = 0;

    /* Open the session in promiscuous mode */
    pcapHandle = pcap_open_live(liveIf, BUFSIZ, 1, 200, errbuf);
    CHECK_NOR_EXIT(NULL == pcapHandle, -1, "Couldn't open if %s \n", liveIf);

    // 调整为非阻塞模式以使用 poll, 目的是当没有流量时，poll 可以通过返回 0 告知
    // 这种场景，我们需要在这时候调用 engine_on_idle 以触发内部的定时器可以让会话继续超时;
    if (pcap_setnonblock(pcapHandle, 1, errbuf) == -1)
    {
        fprintf(stderr, "pcap_setnonblock failed: %s\n", errbuf);
        return -1;
    }
    int nonblock = pcap_getnonblock(pcapHandle, errbuf);
    if (nonblock == -1)
    {
        fprintf(stderr, "pcap_getnonblock failed: %s\n", errbuf);
        return -1;
    }

    /* pcap 过滤条件 */
    lSts = pcap_compile(pcapHandle, &fp, filterExp, 0, 0);
    CHECK_NOR_EXIT(-1 == lSts, -1, "Couldn't parse filter.\n");
    lSts = pcap_setfilter(pcapHandle, &fp);
    CHECK_NOR_EXIT(-1 == lSts, -1, "Counldn't install filter.\n");

    // 保存到输出变量
    *ppLiveCap = pcapHandle;

    // loop
    app_main_ctx* ctx = (app_main_ctx *)userdata;
    struct pollfd pfd[] = {
        {
            .fd = pcap_get_selectable_fd(pcapHandle),
            .events  = POLLIN,
            .revents = POLLIN,
        }
    };

    while (!ctx->stop)
    {
        int pollableFd = poll(&pfd[0], 1, 500); // 100ms 超时
        if (pollableFd > 0)
        {
            pcap_dispatch(pcapHandle, -1, handler, (u_char *)userdata);
            continue;
        }

        if (pollableFd == 0)
        {
            nxt_engine_on_idle(ctx->engine);
        }
    }

    // Free the compiled filter program
    pcap_freecode(&fp);
    pcap_close(pcapHandle);
    return 0;
}

int event_handler_packet_message(nxt_engine_t *engine _U_, nxt_pmessage_t *message, void *userdata _U_)
{
    app_main_ctx* st = (app_main_ctx*)userdata;
    st->precordCnt++;

    precord_t* precord = nxt_message_get_precord(message);
    show_proto_record(precord, st);
    return 0;
}

void show_process_stats(app_main_ctx *ctx)
{
    printf("processed packets: %d\n", ctx->packetCnt);
    printf("output precords: %d\n", ctx->precordCnt);
}

int event_handler_message_just_count(nxt_engine_t *engine _U_, nxt_pmessage_t *message _U_, void *userdata _U_)
{
    app_main_ctx* st = (app_main_ctx*)userdata;
    st->precordCnt++;

    static int sLastCnt = gCtx.packetCnt;
    if (gCtx.packetCnt - sLastCnt > 100000)
    {
        show_process_stats(&gCtx);
        sLastCnt = gCtx.packetCnt;
    }

    return 0;
}

int event_handler_session_message(nxt_engine_t *engine _U_, nxt_pmessage_t *message _U_, void *userdata)
{
    app_main_ctx* st = (app_main_ctx*)userdata;
    st->precordCnt++;

    precord_t* precord = nxt_message_get_precord(message);
    show_proto_record(precord, userdata);

    return 0;
}

int event_handler_dump_error_pkt(nxt_engine_t *engine _U_, nxt_pmessage_t *message, void *userdata)
{
    pcap_dumper_t *dumper = (pcap_dumper_t*)(userdata);

    nxt_mbuf_t *mbuf = nxt_message_get_mbuf(message);
    pcap_pkthdr pkthdr = {{time(0), 0}, (uint32_t)nxt_mbuf_get_length(mbuf), (uint32_t)nxt_mbuf_get_length(mbuf)};
    pcap_dump((u_char *)dumper, &pkthdr, (const u_char *)nxt_mbuf_get_raw(mbuf, 0));
    pcap_dump_flush(dumper);

    return 0;
}

int load_plugins()
{
    // 加载插件
    char errorBuff[255] = { 0 };
    std::string pluginDir(yv::getAppDir() + "plugins");
    const char kPluginPrefix[] = "yaNxtDissector";

    yv::forDirEntry(pluginDir.c_str(), [&](const char *filePath, bool)
    {
        const char *fileName = basename(const_cast<char*>(filePath));
        if (strncmp(fileName, kPluginPrefix, sizeof(kPluginPrefix) - 1) != 0)
        {   // not a yaNxtDissector
            return -1;
        }

        int res = nxt_plugin_dissector_load(filePath, errorBuff, sizeof errorBuff);
        if (res < 0)
        {
            printf("load plugin error: %s, %s\n", filePath, errorBuff);
            return -1;
        }

        printf("load plugin done: %s\n", filePath);
        return 0;
    });

    return 0;
}

int tcp_event_handler(nxt_engine_t *engine _U_, nxt_tcp_segment_t *segment, void *userdata)
{
    pcap_dumper_t *dumper = (pcap_dumper_t *)userdata;
    nxt_mbuf_t    *mbuf   = segment->mbuf;

    pcap_pkthdr pkthdr = {{time(0), 0}, (uint32_t)nxt_mbuf_get_length(mbuf), (uint32_t)nxt_mbuf_get_length(mbuf)};
    pcap_dump((u_char *)dumper, &pkthdr, (const u_char *)nxt_mbuf_get_raw(mbuf, 0));

    return 0;
}

int pcap_open_dumper(const char *filePath, pcap_t **ppPcap, pcap_dumper_t **ppDumper)
{
    *ppPcap   = pcap_open_dead(DLT_EN10MB, 2000);
    *ppDumper = pcap_dump_open(*ppPcap, filePath);
    return 0;
}

int pcap_close_dumper(pcap_t *pcap, pcap_dumper_t *dumper)
{
    if (dumper)
    {
        pcap_dump_close(dumper);
    }

    if (pcap)
    {
        pcap_close(pcap);
    }

    return 0;
}

#ifndef UNIT_TEST
static GOptionEntry gEntries[] =
{
    { "if",          'i', 0, G_OPTION_ARG_STRING, &gCtx.cfg.ifName,          "network interface", "eth0" },
    { "pcap",        'r', 0, G_OPTION_ARG_STRING, &gCtx.cfg.pcapPath,        "pcap file",         "foo.pcap" },
    { "trailer",     't', 0, G_OPTION_ARG_STRING, &gCtx.cfg.trailer,         "trailer type",      "NONE" },
    { "linkname",    'l', 0, G_OPTION_ARG_STRING, &gCtx.cfg.linkName,         "link layer type",  "eth" },
    { "dump",        'd', 0, G_OPTION_ARG_STRING, &gCtx.cfg.dumpPcapPath,    "dump pcap path",    "dump.pcap" },
    { "preload",     'p', 0, G_OPTION_ARG_NONE,   &gCtx.cfg.pcapPreload,     "preload pcap",      NULL },
    { "disable_out", 'n', 0, G_OPTION_ARG_NONE,   &gCtx.cfg.disableOutput,   "disable_output",    NULL },
    { "dump_err_pkt",'e', 0, G_OPTION_ARG_NONE,   &gCtx.cfg.dumpErrPkt,      "dump error pkt",    NULL },
    { "filter_proto",'f', 0, G_OPTION_ARG_STRING, &gCtx.cfg.filterProto,     "filter proto",      ""},
    { "mode",        'm', 0, G_OPTION_ARG_INT,    &gCtx.cfg.runMode,         "run mode: default(0), sdx(1)",      ""},
    { NULL,    0,  0, G_OPTION_ARG_NONE,   NULL,             NULL,                NULL}
};


int main(int argc, char *argv[])
{
    GError         *error   = NULL;
    GOptionContext *context = NULL;
    app_config     *cfgPtr  = &gCtx.cfg;

    // 处理命令行参数
    context = g_option_context_new("- yaLiteDpi");
    g_option_context_add_main_entries (context, gEntries, NULL);
    if (!g_option_context_parse (context, &argc, &argv, &error))
    {
        g_print ("option parsing failed: %s\n", error->message);
        exit (1);
    }

    // 初始化与加载插件
    nxt_init();
    load_plugins();

    // 添加事件处理器
    nxt_cb_event_handler packetHandler = cfgPtr->disableOutput ?
        event_handler_message_just_count : event_handler_packet_message;

    nxt_cb_event_handler sessionHandler = cfgPtr->disableOutput ?
        event_handler_message_just_count : event_handler_session_message;

    // 创建 engine
    nxt_engine_config_t config = {.linkName =  cfgPtr->linkName, .trailerName = cfgPtr->trailer};
    if (cfgPtr->runMode == 1) {
        config.linkName = "sdx";
        // nxt_handoff_mnt_t ipMntAt[] = {
        //     { "sdx", {NXT_HANDOFF_TYPE_NUMBER,  {.number = 0x42}} },
        //     { NULL, { NXT_HANDOFF_TYPE_NONE, {0} } },
        // };
        // nxt_dissector_append_handoff("ipv4", ipMntAt);
    }

    gCtx.engine                = nxt_engine_create(&config);
    if (NULL == gCtx.engine)
    {
        goto error;
    }

    // 在实时模式下安装信号处理函数, 捕获 Ctrl-C 信号;
    signal(SIGINT, handle_signal_stop);
    signal(SIGSEGV, handle_signal_segv);

    // 配置 engineNext 事件处理;
    nxt_engine_add_event_handler(gCtx.engine, NXT_EVENT_PACKET_MESSAGE,  packetHandler, &gCtx);
    nxt_engine_add_event_handler(gCtx.engine, NXT_EVENT_SESSION_MESSAGE, sessionHandler, &gCtx);

    // 处理选项: 准备 error packet dumper
    if (cfgPtr->dumpErrPkt)
    {
        std::string strErrPcapFilename("liteDpi_problem_pkts_");
        strErrPcapFilename.append(std::to_string(time(NULL)));
        strErrPcapFilename.append(".pcap");
        pcap_open_dumper(strErrPcapFilename.c_str(), &gCtx.errorPktPcap, &gCtx.errorPktDumper);
        nxt_engine_add_event_handler(gCtx.engine, NXT_EVENT_EXCEPTION_OCCURRED, event_handler_dump_error_pkt, gCtx.errorPktDumper);
    }

    // 处理选项: dump tcp 重组 packet
    if (cfgPtr->dumpPcapPath != NULL)
    {
        pcap_open_dumper(cfgPtr->dumpPcapPath, &cfgPtr->dumpTcpReassPcap, &cfgPtr->dumpDumper);
        nxt_set_tcp_event_handler(gCtx.engine, tcp_event_handler, cfgPtr->dumpDumper);
        g_free(cfgPtr->dumpPcapPath);
    }

    // 处理选项: proto filter
    if (cfgPtr->filterProto != NULL)
    {
        char *proto = strtok(cfgPtr->filterProto, ",");
        gCtx.filterProtoSet.emplace(proto);
        while ((proto = strtok(NULL, ",")) != NULL)
        {
            gCtx.filterProtoSet.emplace(proto);
        }
        g_free(cfgPtr->filterProto);
    }

    // 解析 pcap 中的 packet;
    if (cfgPtr->pcapPath != NULL)
    {
        gCtx.workMode = WM_PCAP;

        printf("offline mode: read pcap from file %s\n", cfgPtr->pcapPath);
        pcap_packet_foreach(cfgPtr->pcapPath, cfgPtr->pcapPreload, nxt_cb_on_packet, &gCtx);
        g_free(cfgPtr->pcapPath);
    }
    // 解析 live 接口上的 packet;
    else if (cfgPtr->ifName != NULL)
    {
        gCtx.workMode = WM_LIVE;

        printf("live mode: read pcap from if %s\n", cfgPtr->ifName);
        live_packet_foreach(cfgPtr->ifName, pcap_live_cb_on_packet, &gCtx, &cfgPtr->liveCap);
        g_free(cfgPtr->ifName);
    }
    else
    {
        printf("neither -i or -r provied, exit.\n");
        goto out;
    }

    // report
    show_process_stats(&gCtx);

out:
    // 销毁 engine
    nxt_engine_destroy(gCtx.engine);

error:
    nxt_fini();

    g_option_context_free(context);
    if (cfgPtr->dumpDumper)
    {
        pcap_close_dumper(cfgPtr->dumpTcpReassPcap, cfgPtr->dumpDumper);
    }

    if (cfgPtr->dumpErrPkt) pcap_close_dumper(gCtx.errorPktPcap, gCtx.errorPktDumper);
    return 0;
}
#endif