Frame 1: 169 bytes on wire (1352 bits), 169 bytes captured (1352 bits)
User Datagram Protocol, Src Port: 58535, Dst Port: 1719
    Source Port: 58535
    Destination Port: 1719
    Length: 135
    Checksum: 0x3fb1 [unverified]
    [Checksum Status: Unverified]
    [Stream index: 0]
H.225.0 RAS
    RasMessage: admissionRequest (9)
        admissionRequest
            requestSeqNum: 24410
            callType: pointToPoint (0)
                pointToPoint: NULL
            endpointIdentifier: 52B880FC00000003
            destinationInfo: 1 item
                Item 0
                    DestinationInfo item: dialledDigits (0)
                        dialledDigits: 4997450629
            srcInfo: 1 item
                Item 0
                    AliasAddress: h323-ID (1)
                        h323-ID: H-SRV
            srcCallSignalAddress: ipAddress (0)
                ipAddress
                    ip: ***********
                    port: 58535
            bandWidth: 2621
            callReferenceValue: 3436
            conferenceID: 8a3016ff-2901-0010-091c-5c987aa98517
            0... .... activeMC: False
            .0.. .... answerCall: False
            1... .... canMapAlias: True
            callIdentifier
                guid: 8a3016ff-2901-0010-091b-5c987aa98517
            gatekeeperIdentifier: Gk7206
            0... .... willSupplyUUIEs: False

Frame 2: 66 bytes on wire (528 bits), 66 bytes captured (528 bits)
User Datagram Protocol, Src Port: 1719, Dst Port: 58535
    Source Port: 1719
    Destination Port: 58535
    Length: 32
    Checksum: 0xb8a4 [unverified]
    [Checksum Status: Unverified]
    [Stream index: 0]
H.225.0 RAS
    RasMessage: admissionConfirm (10)
        admissionConfirm
            requestSeqNum: 24410
            bandWidth: 2621
            callModel: direct (0)
                direct: NULL
            destCallSignalAddress: ipAddress (0)
                ipAddress
                    ip: ************
                    port: 1720
            irrFrequency: 240
            0... .... willRespondToIRR: False
            uuiesRequested
                .0.. .... setup: False
                ..0. .... callProceeding: False
                ...0 .... connect: False
                .... 0... alerting: False
                .... .0.. information: False
                .... ..0. releaseComplete: False
                .... ...0 facility: False
                0... .... progress: False
                .0.. .... empty: False
    [This is a response to a request in frame 1]
    [RAS Service Response Time: 0.001030000 seconds]

Frame 3: 290 bytes on wire (2320 bits), 290 bytes captured (2320 bits)
Transmission Control Protocol, Src Port: 18742, Dst Port: 1720, Seq: 1, Ack: 1, Len: 236
    Source Port: 18742
    Destination Port: 1720
    [Stream index: 0]
    [TCP Segment Len: 236]
    Sequence number: 1    (relative sequence number)
    [Next sequence number: 237    (relative sequence number)]
    Acknowledgment number: 1    (relative ack number)
    0101 .... = Header Length: 20 bytes (5)
    Flags: 0x010 (ACK)
        000. .... .... = Reserved: Not set
        ...0 .... .... = Nonce: Not set
        .... 0... .... = Congestion Window Reduced (CWR): Not set
        .... .0.. .... = ECN-Echo: Not set
        .... ..0. .... = Urgent: Not set
        .... ...1 .... = Acknowledgment: Set
        .... .... 0... = Push: Not set
        .... .... .0.. = Reset: Not set
        .... .... ..0. = Syn: Not set
        .... .... ...0 = Fin: Not set
        [TCP Flags: ·······A····]
    Window size value: 4128
    [Calculated window size: 4128]
    [Window size scaling factor: -1 (unknown)]
    Checksum: 0xfd2a [unverified]
    [Checksum Status: Unverified]
    Urgent pointer: 0
    [SEQ/ACK analysis]
        [Bytes in flight: 236]
        [Bytes sent since last PSH flag: 236]
    [Timestamps]
        [Time since first frame in this TCP stream: 0.000000000 seconds]
        [Time since previous frame in this TCP stream: 0.000000000 seconds]
    TCP payload (236 bytes)
TPKT, Version: 3, Length: 236
    Version: 3
    Reserved: 0
    Length: 236
Q.931
    Protocol discriminator: Q.931
    Call reference value length: 2
    Call reference flag: Message sent from originating side
    Call reference value: 0d6c
    Message type: SETUP (0x05)
    Bearer capability
        Information element: Bearer capability
        Length: 3
        1... .... = Extension indicator: last octet
        .00. .... = Coding standard: ITU-T standardized coding (0x0)
        ...0 0000 = Information transfer capability: Speech (0x00)
        1... .... = Extension indicator: last octet
        .00. .... = Transfer mode: Circuit mode (0x0)
        ...1 0000 = Information transfer rate: 64 kbit/s (0x10)
        1... .... = Extension indicator: last octet
        .01. .... = Layer identification: Layer 1 identifier (0x1)
        ...0 0101 = User information layer 1 protocol: Recommendation H.221 and H.242 (0x05)
    Calling party number: '4956171777'
        Information element: Calling party number
        Length: 11
        .... 0001 = Numbering plan: E.164 ISDN/telephony numbering (0x1)
        .000 .... = Number type: Unknown (0x0)
        1... .... = Extension indicator: last octet
        Calling party number digits: 4956171777
        E.164 Calling party number digits: 4956171777
    Called party number: '4997450629'
        Information element: Called party number
        Length: 11
        .... 0001 = Numbering plan: E.164 ISDN/telephony numbering (0x1)
        .000 .... = Number type: Unknown (0x0)
        1... .... = Extension indicator: last octet
        Called party number digits: 4997450629
        E.164 Called party number digits: 4997450629
    User-user
        Information element: User-user
        Length: 193
        Protocol discriminator: X.208 and X.209 coded user information (0x05)
H.225.0 CS
    H323-UserInformation
        h323-uu-pdu
            h323-message-body: setup (0)
                setup
                    protocolIdentifier: 0.0.8.2250.0.4 (Version 4)
                    sourceAddress: 1 item
                        Item 0
                            AliasAddress: h323-ID (1)
                                h323-ID: H-SRV
                    sourceInfo
                        .... ...0 mc: False
                        0... .... undefinedNode: False
                    destCallSignalAddress: ipAddress (0)
                        ipAddress
                            ip: ************
                            port: 1720
                    0... .... activeMC: False
                    conferenceID: 8a3016ff-2901-0010-091c-5c987aa98517
                    conferenceGoal: create (0)
                        create: NULL
                    callType: pointToPoint (0)
                        pointToPoint: NULL
                    sourceCallSignalAddress: ipAddress (0)
                        ipAddress
                            ip: ***********
                            port: 1720
                    callIdentifier
                        guid: 8a3016ff-2901-0010-091b-5c987aa98517
                    fastStart: 4 items
                        Item 0
                            FastStart item: 18 octets
                            OpenLogicalChannel
                                forwardLogicalChannelNumber: 1
                                forwardLogicalChannelParameters
                                    dataType: audioData (3)
                                        audioData: g711Ulaw64k (3)
                                            g711Ulaw64k: 20
                                    multiplexParameters: h2250LogicalChannelParameters (3)
                                        h2250LogicalChannelParameters
                                            sessionID: 1
                                            mediaControlChannel: unicastAddress (0)
                                                unicastAddress: iPAddress (0)
                                                    iPAddress
                                                        network: ************
                                                        tsapIdentifier: 40199
                        Item 1
                            FastStart item: 29 octets
                            OpenLogicalChannel
                                forwardLogicalChannelNumber: 1
                                forwardLogicalChannelParameters
                                    dataType: nullData (1)
                                        nullData: NULL
                                    multiplexParameters: none (4)
                                        none: NULL
                                reverseLogicalChannelParameters
                                    dataType: audioData (3)
                                        audioData: g711Ulaw64k (3)
                                            g711Ulaw64k: 20
                                    multiplexParameters: h2250LogicalChannelParameters (2)
                                        h2250LogicalChannelParameters
                                            sessionID: 1
                                            mediaChannel: unicastAddress (0)
                                                unicastAddress: iPAddress (0)
                                                    iPAddress
                                                        network: ************
                                                        tsapIdentifier: 40198
                                            mediaControlChannel: unicastAddress (0)
                                                unicastAddress: iPAddress (0)
                                                    iPAddress
                                                        network: ************
                                                        tsapIdentifier: 40199
                        Item 2
                            FastStart item: 18 octets
                            OpenLogicalChannel
                                forwardLogicalChannelNumber: 2
                                forwardLogicalChannelParameters
                                    dataType: audioData (3)
                                        audioData: g711Alaw64k (1)
                                            g711Alaw64k: 20
                                    multiplexParameters: h2250LogicalChannelParameters (3)
                                        h2250LogicalChannelParameters
                                            sessionID: 1
                                            mediaControlChannel: unicastAddress (0)
                                                unicastAddress: iPAddress (0)
                                                    iPAddress
                                                        network: ************
                                                        tsapIdentifier: 40199
                        Item 3
                            FastStart item: 29 octets
                            OpenLogicalChannel
                                forwardLogicalChannelNumber: 2
                                forwardLogicalChannelParameters
                                    dataType: nullData (1)
                                        nullData: NULL
                                    multiplexParameters: none (4)
                                        none: NULL
                                reverseLogicalChannelParameters
                                    dataType: audioData (3)
                                        audioData: g711Alaw64k (1)
                                            g711Alaw64k: 20
                                    multiplexParameters: h2250LogicalChannelParameters (2)
                                        h2250LogicalChannelParameters
                                            sessionID: 1
                                            mediaChannel: unicastAddress (0)
                                                unicastAddress: iPAddress (0)
                                                    iPAddress
                                                        network: ************
                                                        tsapIdentifier: 40198
                                            mediaControlChannel: unicastAddress (0)
                                                unicastAddress: iPAddress (0)
                                                    iPAddress
                                                        network: ************
                                                        tsapIdentifier: 40199
                    0... .... mediaWaitForConnect: False
                    0... .... canOverlapSend: False
                    1... .... multipleCalls: True
                    0... .... maintainConnection: False
                    symmetricOperationRequired: NULL
            1... .... h245Tunnelling: True

Frame 4: 163 bytes on wire (1304 bits), 163 bytes captured (1304 bits)
Transmission Control Protocol, Src Port: 1720, Dst Port: 18742, Seq: 1, Ack: 237, Len: 109
    Source Port: 1720
    Destination Port: 18742
    [Stream index: 0]
    [TCP Segment Len: 109]
    Sequence number: 1    (relative sequence number)
    [Next sequence number: 110    (relative sequence number)]
    Acknowledgment number: 237    (relative ack number)
    0101 .... = Header Length: 20 bytes (5)
    Flags: 0x018 (PSH, ACK)
        000. .... .... = Reserved: Not set
        ...0 .... .... = Nonce: Not set
        .... 0... .... = Congestion Window Reduced (CWR): Not set
        .... .0.. .... = ECN-Echo: Not set
        .... ..0. .... = Urgent: Not set
        .... ...1 .... = Acknowledgment: Set
        .... .... 1... = Push: Set
        .... .... .0.. = Reset: Not set
        .... .... ..0. = Syn: Not set
        .... .... ...0 = Fin: Not set
        [TCP Flags: ·······AP···]
    Window size value: 4096
    [Calculated window size: 4096]
    [Window size scaling factor: -1 (unknown)]
    Checksum: 0x12ef [unverified]
    [Checksum Status: Unverified]
    Urgent pointer: 0
    [SEQ/ACK analysis]
        [This is an ACK to the segment in frame: 3]
        [The RTT to ACK the segment was: 0.016499000 seconds]
        [Bytes in flight: 109]
        [Bytes sent since last PSH flag: 109]
    [Timestamps]
        [Time since first frame in this TCP stream: 0.016499000 seconds]
        [Time since previous frame in this TCP stream: 0.016499000 seconds]
    TCP payload (109 bytes)
TPKT, Version: 3, Length: 109
    Version: 3
    Reserved: 0
    Length: 109
Q.931
    Protocol discriminator: Q.931
    Call reference value length: 2
    Call reference flag: Message sent to originating side
    Call reference value: 0d6c
    Message type: CALL PROCEEDING (0x02)
    Display  'ADDPAC-4\000'
        Information element: Display
        Length: 9
        Display information: ADDPAC-4
    User-user
        Information element: User-user
        Length: 86
        Protocol discriminator: X.208 and X.209 coded user information (0x05)
H.225.0 CS
    H323-UserInformation
        h323-uu-pdu
            h323-message-body: callProceeding (1)
                callProceeding
                    protocolIdentifier: 0.0.8.2250.0.2 (Version 2)
                    destinationInfo
                        terminal
                        .0.. .... mc: False
                        ..0. .... undefinedNode: False
                    callIdentifier
                        guid: 8a3016ff-2901-0010-091b-5c987aa98517
                    fastStart: 2 items
                        Item 0
                            FastStart item: 25 octets
                            OpenLogicalChannel
                                forwardLogicalChannelNumber: 1
                                forwardLogicalChannelParameters
                                    dataType: audioData (3)
                                        audioData: g711Ulaw64k (3)
                                            g711Ulaw64k: 20
                                    multiplexParameters: h2250LogicalChannelParameters (3)
                                        h2250LogicalChannelParameters
                                            sessionID: 1
                                            mediaChannel: unicastAddress (0)
                                                unicastAddress: iPAddress (0)
                                                    iPAddress
                                                        network: ************
                                                        tsapIdentifier: 23106
                                            mediaControlChannel: unicastAddress (0)
                                                unicastAddress: iPAddress (0)
                                                    iPAddress
                                                        network: ************
                                                        tsapIdentifier: 23107
                        Item 1
                            FastStart item: 22 octets
                            OpenLogicalChannel
                                forwardLogicalChannelNumber: 101
                                forwardLogicalChannelParameters
                                    dataType: nullData (1)
                                        nullData: NULL
                                    multiplexParameters: none (4)
                                        none: NULL
                                reverseLogicalChannelParameters
                                    dataType: audioData (3)
                                        audioData: g711Ulaw64k (3)
                                            g711Ulaw64k: 20
                                    multiplexParameters: h2250LogicalChannelParameters (2)
                                        h2250LogicalChannelParameters
                                            sessionID: 1
                                            mediaControlChannel: unicastAddress (0)
                                                unicastAddress: iPAddress (0)
                                                    iPAddress
                                                        network: ************
                                                        tsapIdentifier: 23107
            1... .... h245Tunnelling: True

Frame 5: 163 bytes on wire (1304 bits), 163 bytes captured (1304 bits)
Transmission Control Protocol, Src Port: 1720, Dst Port: 18742, Seq: 110, Ack: 237, Len: 109
    Source Port: 1720
    Destination Port: 18742
    [Stream index: 0]
    [TCP Segment Len: 109]
    Sequence number: 110    (relative sequence number)
    [Next sequence number: 219    (relative sequence number)]
    Acknowledgment number: 237    (relative ack number)
    0101 .... = Header Length: 20 bytes (5)
    Flags: 0x018 (PSH, ACK)
        000. .... .... = Reserved: Not set
        ...0 .... .... = Nonce: Not set
        .... 0... .... = Congestion Window Reduced (CWR): Not set
        .... .0.. .... = ECN-Echo: Not set
        .... ..0. .... = Urgent: Not set
        .... ...1 .... = Acknowledgment: Set
        .... .... 1... = Push: Set
        .... .... .0.. = Reset: Not set
        .... .... ..0. = Syn: Not set
        .... .... ...0 = Fin: Not set
        [TCP Flags: ·······AP···]
    Window size value: 4096
    [Calculated window size: 4096]
    [Window size scaling factor: -1 (unknown)]
    Checksum: 0x1182 [unverified]
    [Checksum Status: Unverified]
    Urgent pointer: 0
    [SEQ/ACK analysis]
        [Bytes in flight: 218]
        [Bytes sent since last PSH flag: 109]
    [Timestamps]
        [Time since first frame in this TCP stream: 0.020058000 seconds]
        [Time since previous frame in this TCP stream: 0.003559000 seconds]
    TCP payload (109 bytes)
TPKT, Version: 3, Length: 109
    Version: 3
    Reserved: 0
    Length: 109
Q.931
    Protocol discriminator: Q.931
    Call reference value length: 2
    Call reference flag: Message sent to originating side
    Call reference value: 0d6c
    Message type: ALERTING (0x01)
    Display  'ADDPAC-4\000'
        Information element: Display
        Length: 9
        Display information: ADDPAC-4
    User-user
        Information element: User-user
        Length: 86
        Protocol discriminator: X.208 and X.209 coded user information (0x05)
H.225.0 CS
    H323-UserInformation
        h323-uu-pdu
            h323-message-body: alerting (3)
                alerting
                    protocolIdentifier: 0.0.8.2250.0.2 (Version 2)
                    destinationInfo
                        terminal
                        .0.. .... mc: False
                        ..0. .... undefinedNode: False
                    callIdentifier
                        guid: 8a3016ff-2901-0010-091b-5c987aa98517
                    fastStart: 2 items
                        Item 0
                            FastStart item: 25 octets
                            OpenLogicalChannel
                                forwardLogicalChannelNumber: 1
                                forwardLogicalChannelParameters
                                    dataType: audioData (3)
                                        audioData: g711Ulaw64k (3)
                                            g711Ulaw64k: 20
                                    multiplexParameters: h2250LogicalChannelParameters (3)
                                        h2250LogicalChannelParameters
                                            sessionID: 1
                                            mediaChannel: unicastAddress (0)
                                                unicastAddress: iPAddress (0)
                                                    iPAddress
                                                        network: ************
                                                        tsapIdentifier: 23106
                                            mediaControlChannel: unicastAddress (0)
                                                unicastAddress: iPAddress (0)
                                                    iPAddress
                                                        network: ************
                                                        tsapIdentifier: 23107
                        Item 1
                            FastStart item: 22 octets
                            OpenLogicalChannel
                                forwardLogicalChannelNumber: 101
                                forwardLogicalChannelParameters
                                    dataType: nullData (1)
                                        nullData: NULL
                                    multiplexParameters: none (4)
                                        none: NULL
                                reverseLogicalChannelParameters
                                    dataType: audioData (3)
                                        audioData: g711Ulaw64k (3)
                                            g711Ulaw64k: 20
                                    multiplexParameters: h2250LogicalChannelParameters (2)
                                        h2250LogicalChannelParameters
                                            sessionID: 1
                                            mediaControlChannel: unicastAddress (0)
                                                unicastAddress: iPAddress (0)
                                                    iPAddress
                                                        network: ************
                                                        tsapIdentifier: 23107
            1... .... h245Tunnelling: True

Frame 6: 214 bytes on wire (1712 bits), 214 bytes captured (1712 bits)
Transmission Control Protocol, Src Port: 18742, Dst Port: 1720, Seq: 237, Ack: 219, Len: 160
    Source Port: 18742
    Destination Port: 1720
    [Stream index: 0]
    [TCP Segment Len: 160]
    Sequence number: 237    (relative sequence number)
    [Next sequence number: 397    (relative sequence number)]
    Acknowledgment number: 219    (relative ack number)
    0101 .... = Header Length: 20 bytes (5)
    Flags: 0x010 (ACK)
        000. .... .... = Reserved: Not set
        ...0 .... .... = Nonce: Not set
        .... 0... .... = Congestion Window Reduced (CWR): Not set
        .... .0.. .... = ECN-Echo: Not set
        .... ..0. .... = Urgent: Not set
        .... ...1 .... = Acknowledgment: Set
        .... .... 0... = Push: Not set
        .... .... .0.. = Reset: Not set
        .... .... ..0. = Syn: Not set
        .... .... ...0 = Fin: Not set
        [TCP Flags: ·······A····]
    Window size value: 3910
    [Calculated window size: 3910]
    [Window size scaling factor: -1 (unknown)]
    Checksum: 0x8608 [unverified]
    [Checksum Status: Unverified]
    Urgent pointer: 0
    [SEQ/ACK analysis]
        [This is an ACK to the segment in frame: 5]
        [The RTT to ACK the segment was: 0.007354000 seconds]
        [Bytes in flight: 160]
        [Bytes sent since last PSH flag: 396]
    [Timestamps]
        [Time since first frame in this TCP stream: 0.027412000 seconds]
        [Time since previous frame in this TCP stream: 0.007354000 seconds]
    TCP payload (160 bytes)
TPKT, Version: 3, Length: 160
    Version: 3
    Reserved: 0
    Length: 160
Q.931
    Protocol discriminator: Q.931
    Call reference value length: 2
    Call reference flag: Message sent from originating side
    Call reference value: 0d6c
    Message type: FACILITY (0x62)
    Facility
        Type: Facility (0x1c)
        Length: 0
    User-user
        Information element: User-user
        Length: 146
        Protocol discriminator: X.208 and X.209 coded user information (0x05)
H.225.0 CS
    H323-UserInformation
        h323-uu-pdu
            h323-message-body: facility (6)
                facility
                    protocolIdentifier: 0.0.8.2250.0.4 (Version 4)
                    conferenceID: 8a3016ff-2901-0010-091c-5c987aa98517
                    reason: transportedInformation (10)
                        transportedInformation: NULL
                    callIdentifier
                        guid: 8a3016ff-2901-0010-091b-5c987aa98517
                    1... .... multipleCalls: True
                    0... .... maintainConnection: False
                    destinationInfo
                        terminal
                        .0.. .... mc: False
                        ..0. .... undefinedNode: False
            1... .... h245Tunnelling: True
            h245Control: 1 item
                Item 0
                    H245Control item: 82 octets
                    H.245
                        PDU Type: request (0)
                            request: terminalCapabilitySet (2)
                                terminalCapabilitySet
                                    sequenceNumber: 1
                                    protocolIdentifier: *********.0.8 (h245 version 8)
                                    multiplexCapability: h2250Capability (4)
                                        h2250Capability
                                            maximumAudioDelayJitter: 60
                                            receiveMultipointCapability
                                                .0.. .... multicastCapability: False
                                                ..0. .... multiUniCastConference: False
                                                mediaDistributionCapability: 1 item
                                                    Item 0
                                                        MediaDistributionCapability
                                                            ...0 .... centralizedControl: False
                                                            .... 0... distributedControl: False
                                                            .... .0.. centralizedAudio: False
                                                            .... ..0. distributedAudio: False
                                                            .... ...0 centralizedVideo: False
                                                            0... .... distributedVideo: False
                                            transmitMultipointCapability
                                                ..0. .... multicastCapability: False
                                                ...0 .... multiUniCastConference: False
                                                mediaDistributionCapability: 1 item
                                                    Item 0
                                                        MediaDistributionCapability
                                                            ...0 .... centralizedControl: False
                                                            .... 0... distributedControl: False
                                                            .... .0.. centralizedAudio: False
                                                            .... ..0. distributedAudio: False
                                                            .... ...0 centralizedVideo: False
                                                            0... .... distributedVideo: False
                                            receiveAndTransmitMultipointCapability
                                                ..0. .... multicastCapability: False
                                                ...0 .... multiUniCastConference: False
                                                mediaDistributionCapability: 1 item
                                                    Item 0
                                                        MediaDistributionCapability
                                                            ...0 .... centralizedControl: False
                                                            .... 0... distributedControl: False
                                                            .... .0.. centralizedAudio: False
                                                            .... ..0. distributedAudio: False
                                                            .... ...0 centralizedVideo: False
                                                            0... .... distributedVideo: False
                                            mcCapability
                                                ..0. .... centralizedConferenceMC: False
                                                ...0 .... decentralizedConferenceMC: False
                                            .... 0... rtcpVideoControlCapability: False
                                            mediaPacketizationCapability
                                                .... ..0. h261aVideoPacketization: False
                                    capabilityTable: 4 items
                                        Item 0
                                            CapabilityTableEntry
                                                capabilityTableEntryNumber: 1
                                                capability: receiveAndTransmitUserInputCapability (17)
                                                    receiveAndTransmitUserInputCapability: basicString (1)
                                                        basicString: NULL
                                        Item 1
                                            CapabilityTableEntry
                                                capabilityTableEntryNumber: 2
                                                capability: receiveAndTransmitAudioCapability (6)
                                                    receiveAndTransmitAudioCapability: g711Ulaw64k (3)
                                                        g711Ulaw64k: 20
                                        Item 2
                                            CapabilityTableEntry
                                                capabilityTableEntryNumber: 3
                                                capability: receiveAndTransmitAudioCapability (6)
                                                    receiveAndTransmitAudioCapability: g711Alaw64k (1)
                                                        g711Alaw64k: 20
                                        Item 3
                                            CapabilityTableEntry
                                                capabilityTableEntryNumber: 4
                                                capability: receiveRTPAudioTelephonyEventCapability (22)
                                                    receiveRTPAudioTelephonyEventCapability
                                                        dynamicRTPPayloadType: 101
                                                        audioTelephoneEvent: 0123456789ABCD#*
                                    capabilityDescriptors: 1 item
                                        Item 0
                                            CapabilityDescriptor
                                                capabilityDescriptorNumber: 1
                                                simultaneousCapabilities: 3 items
                                                    Item 0
                                                        AlternativeCapabilitySet: 2 items
                                                            Item 0
                                                                alternativeCapability: 2
                                                            Item 1
                                                                alternativeCapability: 3
                                                    Item 1
                                                        AlternativeCapabilitySet: 1 item
                                                            Item 0
                                                                alternativeCapability: 1
                                                    Item 2
                                                        AlternativeCapabilitySet: 1 item
                                                            Item 0
                                                                alternativeCapability: 4

Frame 7: 190 bytes on wire (1520 bits), 190 bytes captured (1520 bits)
Transmission Control Protocol, Src Port: 1720, Dst Port: 18742, Seq: 219, Ack: 397, Len: 136
    Source Port: 1720
    Destination Port: 18742
    [Stream index: 0]
    [TCP Segment Len: 136]
    Sequence number: 219    (relative sequence number)
    [Next sequence number: 355    (relative sequence number)]
    Acknowledgment number: 397    (relative ack number)
    0101 .... = Header Length: 20 bytes (5)
    Flags: 0x018 (PSH, ACK)
        000. .... .... = Reserved: Not set
        ...0 .... .... = Nonce: Not set
        .... 0... .... = Congestion Window Reduced (CWR): Not set
        .... .0.. .... = ECN-Echo: Not set
        .... ..0. .... = Urgent: Not set
        .... ...1 .... = Acknowledgment: Set
        .... .... 1... = Push: Set
        .... .... .0.. = Reset: Not set
        .... .... ..0. = Syn: Not set
        .... .... ...0 = Fin: Not set
        [TCP Flags: ·······AP···]
    Window size value: 4096
    [Calculated window size: 4096]
    [Window size scaling factor: -1 (unknown)]
    Checksum: 0xbfb5 [unverified]
    [Checksum Status: Unverified]
    Urgent pointer: 0
    [SEQ/ACK analysis]
        [This is an ACK to the segment in frame: 6]
        [The RTT to ACK the segment was: 0.013926000 seconds]
        [Bytes in flight: 136]
        [Bytes sent since last PSH flag: 136]
    [Timestamps]
        [Time since first frame in this TCP stream: 0.041338000 seconds]
        [Time since previous frame in this TCP stream: 0.013926000 seconds]
    TCP payload (136 bytes)
TPKT, Version: 3, Length: 136
    Version: 3
    Reserved: 0
    Length: 136
Q.931
    Protocol discriminator: Q.931
    Call reference value length: 2
    Call reference flag: Message sent to originating side
    Call reference value: 0d6c
    Message type: FACILITY (0x62)
    User-user
        Information element: User-user
        Length: 124
        Protocol discriminator: X.208 and X.209 coded user information (0x05)
H.225.0 CS
    H323-UserInformation
        h323-uu-pdu
            h323-message-body: empty (8)
                empty: NULL
            1... .... h245Tunnelling: True
            h245Control: 1 item
                Item 0
                    H245Control item: 112 octets
                    H.245
                        PDU Type: request (0)
                            request: terminalCapabilitySet (2)
                                terminalCapabilitySet
                                    sequenceNumber: 1
                                    protocolIdentifier: *********.0.3 (h245 version 3)
                                    multiplexCapability: h2250Capability (4)
                                        h2250Capability
                                            maximumAudioDelayJitter: 100
                                            receiveMultipointCapability
                                                .0.. .... multicastCapability: False
                                                ..0. .... multiUniCastConference: False
                                                mediaDistributionCapability: 1 item
                                                    Item 0
                                                        MediaDistributionCapability
                                                            ...0 .... centralizedControl: False
                                                            .... 0... distributedControl: False
                                                            .... .0.. centralizedAudio: False
                                                            .... ..0. distributedAudio: False
                                                            .... ...0 centralizedVideo: False
                                                            0... .... distributedVideo: False
                                            transmitMultipointCapability
                                                ..0. .... multicastCapability: False
                                                ...0 .... multiUniCastConference: False
                                                mediaDistributionCapability: 1 item
                                                    Item 0
                                                        MediaDistributionCapability
                                                            ...0 .... centralizedControl: False
                                                            .... 0... distributedControl: False
                                                            .... .0.. centralizedAudio: False
                                                            .... ..0. distributedAudio: False
                                                            .... ...0 centralizedVideo: False
                                                            0... .... distributedVideo: False
                                            receiveAndTransmitMultipointCapability
                                                ..0. .... multicastCapability: False
                                                ...0 .... multiUniCastConference: False
                                                mediaDistributionCapability: 1 item
                                                    Item 0
                                                        MediaDistributionCapability
                                                            ...0 .... centralizedControl: False
                                                            .... 0... distributedControl: False
                                                            .... .0.. centralizedAudio: False
                                                            .... ..0. distributedAudio: False
                                                            .... ...0 centralizedVideo: False
                                                            0... .... distributedVideo: False
                                            mcCapability
                                                ..0. .... centralizedConferenceMC: False
                                                ...0 .... decentralizedConferenceMC: False
                                            .... 0... rtcpVideoControlCapability: False
                                            mediaPacketizationCapability
                                                .... ..0. h261aVideoPacketization: False
                                            0... .... logicalChannelSwitchingCapability: False
                                            0... .... t120DynamicPortCapability: False
                                    capabilityTable: 6 items
                                        Item 0
                                            CapabilityTableEntry
                                                capabilityTableEntryNumber: 1
                                                capability: receiveAudioCapability (4)
                                                    receiveAudioCapability: g711Ulaw64k (3)
                                                        g711Ulaw64k: 20
                                        Item 1
                                            CapabilityTableEntry
                                                capabilityTableEntryNumber: 2
                                                capability: receiveAudioCapability (4)
                                                    receiveAudioCapability: g711Alaw64k (1)
                                                        g711Alaw64k: 20
                                        Item 2
                                            CapabilityTableEntry
                                                capabilityTableEntryNumber: 3
                                                capability: receiveAndTransmitDataApplicationCapability (9)
                                                    receiveAndTransmitDataApplicationCapability
                                                        application: t38fax (12)
                                                            t38fax
                                                                t38FaxProtocol: udp (13)
                                                                    udp: NULL
                                                                t38FaxProfile
                                                                    .0.. .... fillBitRemoval: False
                                                                    ..0. .... transcodingJBIG: False
                                                                    ...0 .... transcodingMMR: False
                                                                    version: 0
                                                                    t38FaxRateManagement: transferredTCF (1)
                                                                        transferredTCF: NULL
                                                                    t38FaxUdpOptions
                                                                        t38FaxMaxBuffer: 200
                                                                        t38FaxMaxDatagram: 72
                                                                        t38FaxUdpEC: t38UDPRedundancy (1)
                                                                            t38UDPRedundancy: NULL
                                                        maxBitRate: 144
                                        Item 3
                                            CapabilityTableEntry
                                                capabilityTableEntryNumber: 4
                                                capability: receiveUserInputCapability (15)
                                                    receiveUserInputCapability: basicString (1)
                                                        basicString: NULL
                                        Item 4
                                            CapabilityTableEntry
                                                capabilityTableEntryNumber: 5
                                                capability: receiveUserInputCapability (15)
                                                    receiveUserInputCapability: hookflash (5)
                                                        hookflash: NULL
                                        Item 5
                                            CapabilityTableEntry
                                                capabilityTableEntryNumber: 6
                                                capability: receiveRTPAudioTelephonyEventCapability (22)
                                                    receiveRTPAudioTelephonyEventCapability
                                                        dynamicRTPPayloadType: 101
                                                        audioTelephoneEvent: 0-15
                                    capabilityDescriptors: 1 item
                                        Item 0
                                            CapabilityDescriptor
                                                capabilityDescriptorNumber: 1
                                                simultaneousCapabilities: 3 items
                                                    Item 0
                                                        AlternativeCapabilitySet: 3 items
                                                            Item 0
                                                                alternativeCapability: 1
                                                            Item 1
                                                                alternativeCapability: 2
                                                            Item 2
                                                                alternativeCapability: 3
                                                    Item 1
                                                        AlternativeCapabilitySet: 2 items
                                                            Item 0
                                                                alternativeCapability: 4
                                                            Item 1
                                                                alternativeCapability: 6
                                                    Item 2
                                                        AlternativeCapabilitySet: 1 item
                                                            Item 0
                                                                alternativeCapability: 5

Frame 8: 138 bytes on wire (1104 bits), 138 bytes captured (1104 bits)
Transmission Control Protocol, Src Port: 18742, Dst Port: 1720, Seq: 397, Ack: 355, Len: 84
    Source Port: 18742
    Destination Port: 1720
    [Stream index: 0]
    [TCP Segment Len: 84]
    Sequence number: 397    (relative sequence number)
    [Next sequence number: 481    (relative sequence number)]
    Acknowledgment number: 355    (relative ack number)
    0101 .... = Header Length: 20 bytes (5)
    Flags: 0x018 (PSH, ACK)
        000. .... .... = Reserved: Not set
        ...0 .... .... = Nonce: Not set
        .... 0... .... = Congestion Window Reduced (CWR): Not set
        .... .0.. .... = ECN-Echo: Not set
        .... ..0. .... = Urgent: Not set
        .... ...1 .... = Acknowledgment: Set
        .... .... 1... = Push: Set
        .... .... .0.. = Reset: Not set
        .... .... ..0. = Syn: Not set
        .... .... ...0 = Fin: Not set
        [TCP Flags: ·······AP···]
    Window size value: 3774
    [Calculated window size: 3774]
    [Window size scaling factor: -1 (unknown)]
    Checksum: 0xd063 [unverified]
    [Checksum Status: Unverified]
    Urgent pointer: 0
    [SEQ/ACK analysis]
        [This is an ACK to the segment in frame: 7]
        [The RTT to ACK the segment was: 0.000342000 seconds]
        [Bytes in flight: 84]
        [Bytes sent since last PSH flag: 480]
    [Timestamps]
        [Time since first frame in this TCP stream: 0.041680000 seconds]
        [Time since previous frame in this TCP stream: 0.000342000 seconds]
    TCP payload (84 bytes)
TPKT, Version: 3, Length: 84
    Version: 3
    Reserved: 0
    Length: 84
Q.931
    Protocol discriminator: Q.931
    Call reference value length: 2
    Call reference flag: Message sent from originating side
    Call reference value: 0d6c
    Message type: FACILITY (0x62)
    Facility
        Type: Facility (0x1c)
        Length: 0
    User-user
        Information element: User-user
        Length: 70
        Protocol discriminator: X.208 and X.209 coded user information (0x05)
H.225.0 CS
    H323-UserInformation
        h323-uu-pdu
            h323-message-body: facility (6)
                facility
                    protocolIdentifier: 0.0.8.2250.0.4 (Version 4)
                    conferenceID: 8a3016ff-2901-0010-091c-5c987aa98517
                    reason: transportedInformation (10)
                        transportedInformation: NULL
                    callIdentifier
                        guid: 8a3016ff-2901-0010-091b-5c987aa98517
                    1... .... multipleCalls: True
                    0... .... maintainConnection: False
                    destinationInfo
                        terminal
                        .0.. .... mc: False
                        ..0. .... undefinedNode: False
            1... .... h245Tunnelling: True
            h245Control: 1 item
                Item 0
                    H245Control item: 6 octets
                    H.245
                        PDU Type: request (0)
                            request: masterSlaveDetermination (1)
                                masterSlaveDetermination
                                    terminalType: 60
                                    statusDeterminationNumber: 9143

Frame 9: 81 bytes on wire (648 bits), 81 bytes captured (648 bits)
Transmission Control Protocol, Src Port: 1720, Dst Port: 18742, Seq: 355, Ack: 481, Len: 27
    Source Port: 1720
    Destination Port: 18742
    [Stream index: 0]
    [TCP Segment Len: 27]
    Sequence number: 355    (relative sequence number)
    [Next sequence number: 382    (relative sequence number)]
    Acknowledgment number: 481    (relative ack number)
    0101 .... = Header Length: 20 bytes (5)
    Flags: 0x018 (PSH, ACK)
        000. .... .... = Reserved: Not set
        ...0 .... .... = Nonce: Not set
        .... 0... .... = Congestion Window Reduced (CWR): Not set
        .... .0.. .... = ECN-Echo: Not set
        .... ..0. .... = Urgent: Not set
        .... ...1 .... = Acknowledgment: Set
        .... .... 1... = Push: Set
        .... .... .0.. = Reset: Not set
        .... .... ..0. = Syn: Not set
        .... .... ...0 = Fin: Not set
        [TCP Flags: ·······AP···]
    Window size value: 4012
    [Calculated window size: 4012]
    [Window size scaling factor: -1 (unknown)]
    Checksum: 0x9c59 [unverified]
    [Checksum Status: Unverified]
    Urgent pointer: 0
    [SEQ/ACK analysis]
        [This is an ACK to the segment in frame: 8]
        [The RTT to ACK the segment was: 0.003663000 seconds]
        [Bytes in flight: 27]
        [Bytes sent since last PSH flag: 27]
    [Timestamps]
        [Time since first frame in this TCP stream: 0.045343000 seconds]
        [Time since previous frame in this TCP stream: 0.003663000 seconds]
    TCP payload (27 bytes)
[Malformed Packet: wxf]
    [Expert Info (Error/Malformed): Malformed Packet (Exception occurred)]
        [Malformed Packet (Exception occurred)]
        [Severity level: Error]
        [Group: Malformed]

Frame 10: 135 bytes on wire (1080 bits), 135 bytes captured (1080 bits)
Transmission Control Protocol, Src Port: 18742, Dst Port: 1720, Seq: 481, Ack: 382, Len: 81
    Source Port: 18742
    Destination Port: 1720
    [Stream index: 0]
    [TCP Segment Len: 81]
    Sequence number: 481    (relative sequence number)
    [Next sequence number: 562    (relative sequence number)]
    Acknowledgment number: 382    (relative ack number)
    0101 .... = Header Length: 20 bytes (5)
    Flags: 0x018 (PSH, ACK)
        000. .... .... = Reserved: Not set
        ...0 .... .... = Nonce: Not set
        .... 0... .... = Congestion Window Reduced (CWR): Not set
        .... .0.. .... = ECN-Echo: Not set
        .... ..0. .... = Urgent: Not set
        .... ...1 .... = Acknowledgment: Set
        .... .... 1... = Push: Set
        .... .... .0.. = Reset: Not set
        .... .... ..0. = Syn: Not set
        .... .... ...0 = Fin: Not set
        [TCP Flags: ·······AP···]
    Window size value: 3747
    [Calculated window size: 3747]
    [Window size scaling factor: -1 (unknown)]
    Checksum: 0x0e96 [unverified]
    [Checksum Status: Unverified]
    Urgent pointer: 0
    [SEQ/ACK analysis]
        [This is an ACK to the segment in frame: 9]
        [The RTT to ACK the segment was: 0.000453000 seconds]
        [Bytes in flight: 81]
        [Bytes sent since last PSH flag: 81]
    [Timestamps]
        [Time since first frame in this TCP stream: 0.045796000 seconds]
        [Time since previous frame in this TCP stream: 0.000453000 seconds]
    TCP payload (81 bytes)
TPKT, Version: 3, Length: 81
    Version: 3
    Reserved: 0
    Length: 81
Q.931
    Protocol discriminator: Q.931
    Call reference value length: 2
    Call reference flag: Message sent from originating side
    Call reference value: 0d6c
    Message type: FACILITY (0x62)
    Facility
        Type: Facility (0x1c)
        Length: 0
    User-user
        Information element: User-user
        Length: 67
        Protocol discriminator: X.208 and X.209 coded user information (0x05)
H.225.0 CS
    H323-UserInformation
        h323-uu-pdu
            h323-message-body: facility (6)
                facility
                    protocolIdentifier: 0.0.8.2250.0.4 (Version 4)
                    conferenceID: 8a3016ff-2901-0010-091c-5c987aa98517
                    reason: transportedInformation (10)
                        transportedInformation: NULL
                    callIdentifier
                        guid: 8a3016ff-2901-0010-091b-5c987aa98517
                    1... .... multipleCalls: True
                    0... .... maintainConnection: False
                    destinationInfo
                        terminal
                        .0.. .... mc: False
                        ..0. .... undefinedNode: False
            1... .... h245Tunnelling: True
            h245Control: 1 item
                Item 0
                    H245Control item: 3 octets
                    H.245
                        PDU Type: response (1)
                            response: terminalCapabilitySetAck (3)
                                terminalCapabilitySetAck
                                    sequenceNumber: 1

Frame 11: 80 bytes on wire (640 bits), 80 bytes captured (640 bits)
Transmission Control Protocol, Src Port: 1720, Dst Port: 18742, Seq: 382, Ack: 562, Len: 26
    Source Port: 1720
    Destination Port: 18742
    [Stream index: 0]
    [TCP Segment Len: 26]
    Sequence number: 382    (relative sequence number)
    [Next sequence number: 408    (relative sequence number)]
    Acknowledgment number: 562    (relative ack number)
    0101 .... = Header Length: 20 bytes (5)
    Flags: 0x018 (PSH, ACK)
        000. .... .... = Reserved: Not set
        ...0 .... .... = Nonce: Not set
        .... 0... .... = Congestion Window Reduced (CWR): Not set
        .... .0.. .... = ECN-Echo: Not set
        .... ..0. .... = Urgent: Not set
        .... ...1 .... = Acknowledgment: Set
        .... .... 1... = Push: Set
        .... .... .0.. = Reset: Not set
        .... .... ..0. = Syn: Not set
        .... .... ...0 = Fin: Not set
        [TCP Flags: ·······AP···]
    Window size value: 4015
    [Calculated window size: 4015]
    [Window size scaling factor: -1 (unknown)]
    Checksum: 0x9def [unverified]
    [Checksum Status: Unverified]
    Urgent pointer: 0
    [SEQ/ACK analysis]
        [This is an ACK to the segment in frame: 10]
        [The RTT to ACK the segment was: 0.007830000 seconds]
        [Bytes in flight: 26]
        [Bytes sent since last PSH flag: 26]
    [Timestamps]
        [Time since first frame in this TCP stream: 0.053626000 seconds]
        [Time since previous frame in this TCP stream: 0.007830000 seconds]
    TCP payload (26 bytes)
[Malformed Packet: wxf]
    [Expert Info (Error/Malformed): Malformed Packet (Exception occurred)]
        [Malformed Packet (Exception occurred)]
        [Severity level: Error]
        [Group: Malformed]

Frame 12: 134 bytes on wire (1072 bits), 134 bytes captured (1072 bits)
Transmission Control Protocol, Src Port: 18742, Dst Port: 1720, Seq: 562, Ack: 408, Len: 80
    Source Port: 18742
    Destination Port: 1720
    [Stream index: 0]
    [TCP Segment Len: 80]
    Sequence number: 562    (relative sequence number)
    [Next sequence number: 642    (relative sequence number)]
    Acknowledgment number: 408    (relative ack number)
    0101 .... = Header Length: 20 bytes (5)
    Flags: 0x010 (ACK)
        000. .... .... = Reserved: Not set
        ...0 .... .... = Nonce: Not set
        .... 0... .... = Congestion Window Reduced (CWR): Not set
        .... .0.. .... = ECN-Echo: Not set
        .... ..0. .... = Urgent: Not set
        .... ...1 .... = Acknowledgment: Set
        .... .... 0... = Push: Not set
        .... .... .0.. = Reset: Not set
        .... .... ..0. = Syn: Not set
        .... .... ...0 = Fin: Not set
        [TCP Flags: ·······A····]
    Window size value: 3721
    [Calculated window size: 3721]
    [Window size scaling factor: -1 (unknown)]
    Checksum: 0x1032 [unverified]
    [Checksum Status: Unverified]
    Urgent pointer: 0
    [SEQ/ACK analysis]
        [This is an ACK to the segment in frame: 11]
        [The RTT to ACK the segment was: 0.002049000 seconds]
        [Bytes in flight: 80]
        [Bytes sent since last PSH flag: 80]
    [Timestamps]
        [Time since first frame in this TCP stream: 0.055675000 seconds]
        [Time since previous frame in this TCP stream: 0.002049000 seconds]
    TCP payload (80 bytes)
TPKT, Version: 3, Length: 80
    Version: 3
    Reserved: 0
    Length: 80
Q.931
    Protocol discriminator: Q.931
    Call reference value length: 2
    Call reference flag: Message sent from originating side
    Call reference value: 0d6c
    Message type: FACILITY (0x62)
    Facility
        Type: Facility (0x1c)
        Length: 0
    User-user
        Information element: User-user
        Length: 66
        Protocol discriminator: X.208 and X.209 coded user information (0x05)
H.225.0 CS
    H323-UserInformation
        h323-uu-pdu
            h323-message-body: facility (6)
                facility
                    protocolIdentifier: 0.0.8.2250.0.4 (Version 4)
                    conferenceID: 8a3016ff-2901-0010-091c-5c987aa98517
                    reason: transportedInformation (10)
                        transportedInformation: NULL
                    callIdentifier
                        guid: 8a3016ff-2901-0010-091b-5c987aa98517
                    1... .... multipleCalls: True
                    0... .... maintainConnection: False
                    destinationInfo
                        terminal
                        .0.. .... mc: False
                        ..0. .... undefinedNode: False
            1... .... h245Tunnelling: True
            h245Control: 1 item
                Item 0
                    H245Control item: 2 octets
                    H.245
                        PDU Type: response (1)
                            response: masterSlaveDeterminationAck (1)
                                masterSlaveDeterminationAck
                                    decision: slave (1)
                                        slave: NULL

Frame 13: 207 bytes on wire (1656 bits), 207 bytes captured (1656 bits)
Transmission Control Protocol, Src Port: 1720, Dst Port: 18742, Seq: 408, Ack: 642, Len: 153
    Source Port: 1720
    Destination Port: 18742
    [Stream index: 0]
    [TCP Segment Len: 153]
    Sequence number: 408    (relative sequence number)
    [Next sequence number: 561    (relative sequence number)]
    Acknowledgment number: 642    (relative ack number)
    0101 .... = Header Length: 20 bytes (5)
    Flags: 0x018 (PSH, ACK)
        000. .... .... = Reserved: Not set
        ...0 .... .... = Nonce: Not set
        .... 0... .... = Congestion Window Reduced (CWR): Not set
        .... .0.. .... = ECN-Echo: Not set
        .... ..0. .... = Urgent: Not set
        .... ...1 .... = Acknowledgment: Set
        .... .... 1... = Push: Set
        .... .... .0.. = Reset: Not set
        .... .... ..0. = Syn: Not set
        .... .... ...0 = Fin: Not set
        [TCP Flags: ·······AP···]
    Window size value: 4096
    [Calculated window size: 4096]
    [Window size scaling factor: -1 (unknown)]
    Checksum: 0xc61f [unverified]
    [Checksum Status: Unverified]
    Urgent pointer: 0
    [SEQ/ACK analysis]
        [This is an ACK to the segment in frame: 12]
        [The RTT to ACK the segment was: 4.652732000 seconds]
        [Bytes in flight: 153]
        [Bytes sent since last PSH flag: 153]
    [Timestamps]
        [Time since first frame in this TCP stream: 4.708407000 seconds]
        [Time since previous frame in this TCP stream: 4.652732000 seconds]
    TCP payload (153 bytes)
TPKT, Version: 3, Length: 153
    Version: 3
    Reserved: 0
    Length: 153
Q.931
    Protocol discriminator: Q.931
    Call reference value length: 2
    Call reference flag: Message sent to originating side
    Call reference value: 0d6c
    Message type: CONNECT (0x07)
    Bearer capability
        Information element: Bearer capability
        Length: 3
        1... .... = Extension indicator: last octet
        .00. .... = Coding standard: ITU-T standardized coding (0x0)
        ...0 0000 = Information transfer capability: Speech (0x00)
        1... .... = Extension indicator: last octet
        .00. .... = Transfer mode: Circuit mode (0x0)
        ...1 0000 = Information transfer rate: 64 kbit/s (0x10)
        1... .... = Extension indicator: last octet
        .01. .... = Layer identification: Layer 1 identifier (0x1)
        ...0 0011 = User information layer 1 protocol: Recommendation G.711 A-law (0x03)
    Display  'ADDPAC-4\000'
        Information element: Display
        Length: 9
        Display information: ADDPAC-4
    User-user
        Information element: User-user
        Length: 125
        Protocol discriminator: X.208 and X.209 coded user information (0x05)
H.225.0 CS
    H323-UserInformation
        h323-uu-pdu
            h323-message-body: connect (2)
                connect
                    protocolIdentifier: 0.0.8.2250.0.2 (Version 2)
                    destinationInfo
                        vendor
                            vendor
                                t35CountryCode: Korea (Rep. of) (97)
                                t35Extension: 0
                                manufacturerCode: 22
                            H.225 Manufacturer: Unknown (0x61000016)
                            productId: AddPac VoIP
                            versionId: 8.23
                        terminal
                        ..0. .... mc: False
                        ...0 .... undefinedNode: False
                    conferenceID: 8a3016ff-2901-0010-091c-5c987aa98517
                    callIdentifier
                        guid: 8a3016ff-2901-0010-091b-5c987aa98517
                    fastStart: 2 items
                        Item 0
                            FastStart item: 25 octets
                            OpenLogicalChannel
                                forwardLogicalChannelNumber: 1
                                forwardLogicalChannelParameters
                                    dataType: audioData (3)
                                        audioData: g711Ulaw64k (3)
                                            g711Ulaw64k: 20
                                    multiplexParameters: h2250LogicalChannelParameters (3)
                                        h2250LogicalChannelParameters
                                            sessionID: 1
                                            mediaChannel: unicastAddress (0)
                                                unicastAddress: iPAddress (0)
                                                    iPAddress
                                                        network: ************
                                                        tsapIdentifier: 23106
                                            mediaControlChannel: unicastAddress (0)
                                                unicastAddress: iPAddress (0)
                                                    iPAddress
                                                        network: ************
                                                        tsapIdentifier: 23107
                        Item 1
                            FastStart item: 22 octets
                            OpenLogicalChannel
                                forwardLogicalChannelNumber: 101
                                forwardLogicalChannelParameters
                                    dataType: nullData (1)
                                        nullData: NULL
                                    multiplexParameters: none (4)
                                        none: NULL
                                reverseLogicalChannelParameters
                                    dataType: audioData (3)
                                        audioData: g711Ulaw64k (3)
                                            g711Ulaw64k: 20
                                    multiplexParameters: h2250LogicalChannelParameters (2)
                                        h2250LogicalChannelParameters
                                            sessionID: 1
                                            mediaControlChannel: unicastAddress (0)
                                                unicastAddress: iPAddress (0)
                                                    iPAddress
                                                        network: ************
                                                        tsapIdentifier: 23107
            1... .... h245Tunnelling: True

Frame 18: 208 bytes on wire (1664 bits), 208 bytes captured (1664 bits)
Transmission Control Protocol, Src Port: 18742, Dst Port: 1720, Seq: 642, Ack: 561, Len: 154
    Source Port: 18742
    Destination Port: 1720
    [Stream index: 0]
    [TCP Segment Len: 154]
    Sequence number: 642    (relative sequence number)
    [Next sequence number: 796    (relative sequence number)]
    Acknowledgment number: 561    (relative ack number)
    0101 .... = Header Length: 20 bytes (5)
    Flags: 0x010 (ACK)
        000. .... .... = Reserved: Not set
        ...0 .... .... = Nonce: Not set
        .... 0... .... = Congestion Window Reduced (CWR): Not set
        .... .0.. .... = ECN-Echo: Not set
        .... ..0. .... = Urgent: Not set
        .... ...1 .... = Acknowledgment: Set
        .... .... 0... = Push: Not set
        .... .... .0.. = Reset: Not set
        .... .... ..0. = Syn: Not set
        .... .... ...0 = Fin: Not set
        [TCP Flags: ·······A····]
    Window size value: 4128
    [Calculated window size: 4128]
    [Window size scaling factor: -1 (unknown)]
    Checksum: 0xe3f8 [unverified]
    [Checksum Status: Unverified]
    Urgent pointer: 0
    [SEQ/ACK analysis]
        [This is an ACK to the segment in frame: 13]
        [The RTT to ACK the segment was: 2.001355000 seconds]
        [Bytes in flight: 154]
        [Bytes sent since last PSH flag: 234]
    [Timestamps]
        [Time since first frame in this TCP stream: 6.709762000 seconds]
        [Time since previous frame in this TCP stream: 2.001355000 seconds]
    TCP payload (154 bytes)
TPKT, Version: 3, Length: 154
    Version: 3
    Reserved: 0
    Length: 154
Q.931
    Protocol discriminator: Q.931
    Call reference value length: 2
    Call reference flag: Message sent from originating side
    Call reference value: 0d6c
    Message type: FACILITY (0x62)
    Facility
        Type: Facility (0x1c)
        Length: 0
    User-user
        Information element: User-user
        Length: 140
        Protocol discriminator: X.208 and X.209 coded user information (0x05)
H.225.0 CS
    H323-UserInformation
        h323-uu-pdu
            h323-message-body: facility (6)
                facility
                    protocolIdentifier: 0.0.8.2250.0.4 (Version 4)
                    conferenceID: 8a3016ff-2901-0010-091c-5c987aa98517
                    reason: transportedInformation (10)
                        transportedInformation: NULL
                    callIdentifier
                        guid: 8a3016ff-2901-0010-091b-5c987aa98517
                    1... .... multipleCalls: True
                    0... .... maintainConnection: False
                    destinationInfo
                        vendor
                            vendor
                                t35CountryCode: Korea (Rep. of) (97)
                                t35Extension: 0
                                manufacturerCode: 22
                            H.225 Manufacturer: Unknown (0x61000016)
                            productId: AddPac VoIP
                            versionId: 8.23
                        terminal
                        ..0. .... mc: False
                        ...0 .... undefinedNode: False
            1... .... h245Tunnelling: True
            h245Control: 1 item
                Item 0
                    H245Control item: 54 octets
                    H.245
                        PDU Type: request (0)
                            request: terminalCapabilitySet (2)
                                terminalCapabilitySet
                                    sequenceNumber: 2
                                    protocolIdentifier: *********.0.8 (h245 version 8)
                                    multiplexCapability: h2250Capability (4)
                                        h2250Capability
                                            maximumAudioDelayJitter: 60
                                            receiveMultipointCapability
                                                .0.. .... multicastCapability: False
                                                ..0. .... multiUniCastConference: False
                                                mediaDistributionCapability: 1 item
                                                    Item 0
                                                        MediaDistributionCapability
                                                            ...0 .... centralizedControl: False
                                                            .... 0... distributedControl: False
                                                            .... .0.. centralizedAudio: False
                                                            .... ..0. distributedAudio: False
                                                            .... ...0 centralizedVideo: False
                                                            0... .... distributedVideo: False
                                            transmitMultipointCapability
                                                ..0. .... multicastCapability: False
                                                ...0 .... multiUniCastConference: False
                                                mediaDistributionCapability: 1 item
                                                    Item 0
                                                        MediaDistributionCapability
                                                            ...0 .... centralizedControl: False
                                                            .... 0... distributedControl: False
                                                            .... .0.. centralizedAudio: False
                                                            .... ..0. distributedAudio: False
                                                            .... ...0 centralizedVideo: False
                                                            0... .... distributedVideo: False
                                            receiveAndTransmitMultipointCapability
                                                ..0. .... multicastCapability: False
                                                ...0 .... multiUniCastConference: False
                                                mediaDistributionCapability: 1 item
                                                    Item 0
                                                        MediaDistributionCapability
                                                            ...0 .... centralizedControl: False
                                                            .... 0... distributedControl: False
                                                            .... .0.. centralizedAudio: False
                                                            .... ..0. distributedAudio: False
                                                            .... ...0 centralizedVideo: False
                                                            0... .... distributedVideo: False
                                            mcCapability
                                                ..0. .... centralizedConferenceMC: False
                                                ...0 .... decentralizedConferenceMC: False
                                            .... 0... rtcpVideoControlCapability: False
                                            mediaPacketizationCapability
                                                .... ..0. h261aVideoPacketization: False
                                    capabilityTable: 4 items
                                        Item 0
                                            CapabilityTableEntry
                                                capabilityTableEntryNumber: 1
                                                capability: receiveAndTransmitUserInputCapability (17)
                                                    receiveAndTransmitUserInputCapability: basicString (1)
                                                        basicString: NULL
                                        Item 1
                                            CapabilityTableEntry
                                                capabilityTableEntryNumber: 2
                                                capability: receiveAndTransmitAudioCapability (6)
                                                    receiveAndTransmitAudioCapability: g711Ulaw64k (3)
                                                        g711Ulaw64k: 20
                                        Item 2
                                            CapabilityTableEntry
                                                capabilityTableEntryNumber: 3
                                        Item 3
                                            CapabilityTableEntry
                                                capabilityTableEntryNumber: 4
                                    capabilityDescriptors: 1 item
                                        Item 0
                                            CapabilityDescriptor
                                                capabilityDescriptorNumber: 1
                                                simultaneousCapabilities: 2 items
                                                    Item 0
                                                        AlternativeCapabilitySet: 1 item
                                                            Item 0
                                                                alternativeCapability: 2
                                                    Item 1
                                                        AlternativeCapabilitySet: 1 item
                                                            Item 0
                                                                alternativeCapability: 1

Frame 19: 81 bytes on wire (648 bits), 81 bytes captured (648 bits)
Transmission Control Protocol, Src Port: 1720, Dst Port: 18742, Seq: 561, Ack: 796, Len: 27
    Source Port: 1720
    Destination Port: 18742
    [Stream index: 0]
    [TCP Segment Len: 27]
    Sequence number: 561    (relative sequence number)
    [Next sequence number: 588    (relative sequence number)]
    Acknowledgment number: 796    (relative ack number)
    0101 .... = Header Length: 20 bytes (5)
    Flags: 0x018 (PSH, ACK)
        000. .... .... = Reserved: Not set
        ...0 .... .... = Nonce: Not set
        .... 0... .... = Congestion Window Reduced (CWR): Not set
        .... .0.. .... = ECN-Echo: Not set
        .... ..0. .... = Urgent: Not set
        .... ...1 .... = Acknowledgment: Set
        .... .... 1... = Push: Set
        .... .... .0.. = Reset: Not set
        .... .... ..0. = Syn: Not set
        .... .... ...0 = Fin: Not set
        [TCP Flags: ·······AP···]
    Window size value: 4096
    [Calculated window size: 4096]
    [Window size scaling factor: -1 (unknown)]
    Checksum: 0x98fc [unverified]
    [Checksum Status: Unverified]
    Urgent pointer: 0
    [SEQ/ACK analysis]
        [This is an ACK to the segment in frame: 18]
        [The RTT to ACK the segment was: 0.010819000 seconds]
        [Bytes in flight: 27]
        [Bytes sent since last PSH flag: 27]
    [Timestamps]
        [Time since first frame in this TCP stream: 6.720581000 seconds]
        [Time since previous frame in this TCP stream: 0.010819000 seconds]
    TCP payload (27 bytes)
[Malformed Packet: wxf]
    [Expert Info (Error/Malformed): Malformed Packet (Exception occurred)]
        [Malformed Packet (Exception occurred)]
        [Severity level: Error]
        [Group: Malformed]

Frame 20: 103 bytes on wire (824 bits), 103 bytes captured (824 bits)
Transmission Control Protocol, Src Port: 1720, Dst Port: 18742, Seq: 588, Ack: 796, Len: 49
    Source Port: 1720
    Destination Port: 18742
    [Stream index: 0]
    [TCP Segment Len: 49]
    Sequence number: 588    (relative sequence number)
    [Next sequence number: 637    (relative sequence number)]
    Acknowledgment number: 796    (relative ack number)
    0101 .... = Header Length: 20 bytes (5)
    Flags: 0x018 (PSH, ACK)
        000. .... .... = Reserved: Not set
        ...0 .... .... = Nonce: Not set
        .... 0... .... = Congestion Window Reduced (CWR): Not set
        .... .0.. .... = ECN-Echo: Not set
        .... ..0. .... = Urgent: Not set
        .... ...1 .... = Acknowledgment: Set
        .... .... 1... = Push: Set
        .... .... .0.. = Reset: Not set
        .... .... ..0. = Syn: Not set
        .... .... ...0 = Fin: Not set
        [TCP Flags: ·······AP···]
    Window size value: 4096
    [Calculated window size: 4096]
    [Window size scaling factor: -1 (unknown)]
    Checksum: 0xbb0b [unverified]
    [Checksum Status: Unverified]
    Urgent pointer: 0
    [SEQ/ACK analysis]
        [Bytes in flight: 76]
        [Bytes sent since last PSH flag: 49]
    [Timestamps]
        [Time since first frame in this TCP stream: 9.553154000 seconds]
        [Time since previous frame in this TCP stream: 2.832573000 seconds]
    TCP payload (49 bytes)
TPKT, Version: 3, Length: 49
    Version: 3
    Reserved: 0
    Length: 49
Q.931
    Protocol discriminator: Q.931
    Call reference value length: 2
    Call reference flag: Message sent to originating side
    Call reference value: 0d6c
    Message type: RELEASE COMPLETE (0x5a)
    Cause
        Information element: Cause
        Length: 2
        .... 0000 = Cause location: User (U) (0)
        .00. .... = Coding standard: ITU-T standardized coding (0x0)
        1... .... = Extension indicator: last octet
        .001 0000 = Cause value: Normal call clearing (16)
        1... .... = Extension indicator: last octet
    User-user
        Information element: User-user
        Length: 33
        Protocol discriminator: X.208 and X.209 coded user information (0x05)
H.225.0 CS
    H323-UserInformation
        h323-uu-pdu
            h323-message-body: releaseComplete (5)
                releaseComplete
                    protocolIdentifier: 0.0.8.2250.0.2 (Version 2)
                    callIdentifier
                        guid: 8a3016ff-2901-0010-091b-5c987aa98517
            1... .... h245Tunnelling: True

Frame 21: 133 bytes on wire (1064 bits), 133 bytes captured (1064 bits)
User Datagram Protocol, Src Port: 58535, Dst Port: 1719
    Source Port: 58535
    Destination Port: 1719
    Length: 99
    Checksum: 0x6a1e [unverified]
    [Checksum Status: Unverified]
    [Stream index: 0]
H.225.0 RAS
    RasMessage: disengageRequest (15)
        disengageRequest
            requestSeqNum: 24411
            endpointIdentifier: 52B880FC00000003
            conferenceID: 8a3016ff-2901-0010-091c-5c987aa98517
            callReferenceValue: 3436
            disengageReason: normalDrop (1)
                normalDrop: NULL
            callIdentifier
                guid: 8a3016ff-2901-0010-091b-5c987aa98517
            gatekeeperIdentifier: Gk7206
            0... .... answeredCall: False

Frame 22: 60 bytes on wire (480 bits), 60 bytes captured (480 bits)
    Encapsulation type: Ethernet (1)
    Arrival Time: Jul 23, 2010 19:35:37.143917000 CST
    [Time shift for this packet: 0.000000000 seconds]
    Epoch Time: 1279884937.143917000 seconds
    [Time delta from previous captured frame: 0.000814000 seconds]
    [Time delta from previous displayed frame: 0.000814000 seconds]
    [Time since reference or first frame: 9.558598000 seconds]
    Frame Number: 22
    Frame Length: 60 bytes (480 bits)
    Capture Length: 60 bytes (480 bits)
    [Frame is marked: False]
    [Frame is ignored: False]
    [Protocols in frame: Ethernet.Ethertype.IPv4.UDP.H.225.0]
Ethernet II, Src: 00:19:e8:05:d2:1b (00:19:e8:05:d2:1b), Dst: 88:43:e1:9a:4b:01 (88:43:e1:9a:4b:01)
    Destination: 88:43:e1:9a:4b:01 (88:43:e1:9a:4b:01)
        Address: 88:43:e1:9a:4b:01 (88:43:e1:9a:4b:01)
        .... ..0. .... .... .... .... = LG bit: Globally unique address (factory default)
        .... ...0 .... .... .... .... = IG bit: Individual address (unicast)
    Source: 00:19:e8:05:d2:1b (00:19:e8:05:d2:1b)
        Address: 00:19:e8:05:d2:1b (00:19:e8:05:d2:1b)
        .... ..0. .... .... .... .... = LG bit: Globally unique address (factory default)
        .... ...0 .... .... .... .... = IG bit: Individual address (unicast)
    Type: IPv4 (0x0800)
    Padding: 000000000000000000000000000000
Internet Protocol Version 4, Src: ***********, Dst: ***********
    0100 .... = Version: 4
    .... 0101 = Header Length: 20 bytes (5)
    Differentiated Services Field: 0x00 (DSCP: CS0, ECN: Not-ECT)
        0000 00.. = Differentiated Services Codepoint: Default (0)
        .... ..00 = Explicit Congestion Notification: Not ECN-Capable Transport (0)
    Total Length: 31
    Identification: 0x5835 (22581)
    Flags: 0x0000
        0... .... .... .... = Reserved bit: Not set
        .0.. .... .... .... = Don't fragment: Not set
        ..0. .... .... .... = More fragments: Not set
        ...0 0000 0000 0000 = Fragment offset: 0
    Time to live: 255
    Protocol: UDP (17)
    Header checksum: 0xf663 [validation disabled]
    [Header checksum status: Unverified]
    Source: ***********
    Destination: ***********
User Datagram Protocol, Src Port: 1719, Dst Port: 58535
    Source Port: 1719
    Destination Port: 58535
    Length: 11
    Checksum: 0x0ce5 [unverified]
    [Checksum Status: Unverified]
    [Stream index: 0]
H.225.0 RAS
    RasMessage: disengageConfirm (16)
        disengageConfirm
            requestSeqNum: 24411
    [This is a response to a request in frame 21]
    [RAS Service Response Time: 0.000814000 seconds]

